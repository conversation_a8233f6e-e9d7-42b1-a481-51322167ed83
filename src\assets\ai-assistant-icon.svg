<?xml version="1.0" encoding="UTF-8"?>
<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆形 -->
  <circle cx="40" cy="40" r="38" fill="url(#gradient)" stroke="#E0E7FF" stroke-width="2"/>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 机器人头部 -->
  <rect x="25" y="20" width="30" height="25" rx="4" fill="white" opacity="0.9"/>
  
  <!-- 眼睛 -->
  <circle cx="32" cy="30" r="3" fill="#667eea"/>
  <circle cx="48" cy="30" r="3" fill="#667eea"/>
  
  <!-- 嘴巴 -->
  <rect x="35" y="37" width="10" height="2" rx="1" fill="#667eea"/>
  
  <!-- 天线 -->
  <line x1="35" y1="20" x2="35" y2="15" stroke="white" stroke-width="2" stroke-linecap="round"/>
  <line x1="45" y1="20" x2="45" y2="15" stroke="white" stroke-width="2" stroke-linecap="round"/>
  <circle cx="35" cy="13" r="2" fill="white"/>
  <circle cx="45" cy="13" r="2" fill="white"/>
  
  <!-- 身体 -->
  <rect x="28" y="45" width="24" height="20" rx="3" fill="white" opacity="0.9"/>
  
  <!-- 胸前指示灯 -->
  <circle cx="40" cy="55" r="2" fill="#667eea"/>
  
  <!-- 手臂 -->
  <rect x="20" y="48" width="8" height="3" rx="1.5" fill="white" opacity="0.9"/>
  <rect x="52" y="48" width="8" height="3" rx="1.5" fill="white" opacity="0.9"/>
</svg>
